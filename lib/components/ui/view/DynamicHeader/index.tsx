"use client"
import React, { useState } from "react"
import { Link, usePathname } from "@i18n/navigation"
import { LanguageSelector } from "../LanguageSelector"
import { ThemeToggle } from "../ThemeToggle"
import { HeaderSearch } from "../HeaderSearch"
import { Icon } from "@/lib/components/common/Icon"
import { SiteSettings, NavItem } from "@/lib/types"
import { cn } from "@/lib/utils/react"
// 使用NavItem作为MenuItem类型
type MenuItem = NavItem

interface DynamicHeaderProps {
	navItem: NavItem[]
	siteSettings?: SiteSettings
}

export const DynamicHeader: React.FC<DynamicHeaderProps> = ({
	navItem,
	siteSettings,
}) => {
	const pathname = usePathname()
	const [isMenuOpen, setIsMenuOpen] = useState(false)
	// 移动端菜单项展开状态管理
	const [mobileMenuStates, setMobileMenuStates] = useState<Record<string, boolean>>({})

	// 切换移动菜单
	const toggleMenu = () => {
		setIsMenuOpen(!isMenuOpen)
	}

	// 切换移动端菜单项展开状态
	const toggleMobileMenuItem = (itemId: string) => {
		setMobileMenuStates(prev => ({
			...prev,
			[itemId]: !prev[itemId]
		}))
	}

	// 检查链接是否激活
	const isActive = (href: string) => {
		if (href === "/") {
			return pathname === href
		}
		return pathname === href || pathname.startsWith(`${href}/`)
	}

	// 渲染菜单项
	const renderMenuItem = (item: MenuItem, level = 0) => {
		const active = isActive(item.href)
		const hasChildren = item.children && item.children.length > 0

		// 根据级别应用不同的样式
		const linkClasses = cn(
			level === 0
				? {
						"inline-flex items-center px-4 py-2 rounded-lg transition-all": true,
						"bg-accent/30 text-primary-foreground": active,
						"hover:scale-105 hover:bg-accent hover:text-accent-foreground":
							!active,
					}
				: {
						"block px-5 py-2.5 hover:bg-accent hover:text-accent-foreground rounded-lg mx-2 transition-all duration-150 flex items-center": true,
					},
		)

		return (
			<div
				key={item.id}
				className={cn(level === 0 ? "relative group" : "relative group/sub")}
			>
				<Link href={item.href} className={linkClasses}>
					{item.iconName && (
						<Icon name={item.iconName} className="h-5 w-5 mr-1" size={20} />
					)}
					<span>{item.label}</span>
					{hasChildren &&
						(level === 0 ? (
							<Icon name="ChevronDown" className="h-4 w-4 ml-1" size={16} />
						) : (
							<Icon
								name="ChevronRight"
								className="h-4 w-4 ml-1 transform group-hover/sub:translate-x-1 transition-transform"
								size={16}
							/>
						))}
				</Link>

				{/* 子菜单 */}
				{hasChildren && (
					<div
						className={
							level === 0
								? "absolute left-0 mt-1 w-56 bg-card/95 backdrop-blur-sm rounded-xl shadow-xl border border-border/50 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 transform origin-top scale-95 group-hover:scale-100 z-50"
								: "absolute left-full top-0 w-56 bg-card/95 backdrop-blur-sm rounded-xl shadow-xl border border-border/50 opacity-0 invisible group-hover/sub:opacity-100 group-hover/sub:visible transition-all duration-200 transform -translate-x-2 group-hover/sub:translate-x-1 origin-left scale-95 group-hover/sub:scale-100 ml-1"
						}
					>
						<div className="py-3 text-card-foreground text-base">
							{item.children?.map((child) => renderMenuItem(child, level + 1))}
						</div>
					</div>
				)}
			</div>
		)
	}

	// 渲染移动菜单项
	const renderMobileMenuItem = (item: MenuItem, level = 0) => {
		const active = isActive(item.href)
		const hasChildren = item.children && item.children.length > 0
		const isOpen = mobileMenuStates[item.id] || false

		return (
			<div key={item.id} className="w-full">
				<div className="flex items-center justify-between w-full">
					<Link
						href={item.href}
						className={`flex items-center py-2 px-4 ${active ? "text-primary" : ""} ${level > 0 ? `pl-${4 + level * 4}` : ""}`}
						onClick={() => !hasChildren && setIsMenuOpen(false)}
					>
						{item.iconName && (
							<Icon name={item.iconName} className="h-5 w-5 mr-2" size={20} />
						)}
						<span>{item.label}</span>
					</Link>
					{hasChildren && (
						<button
							type="button"
							onClick={() => toggleMobileMenuItem(item.id)}
							className="p-2 text-foreground/70 hover:text-foreground"
							title={item.label}
						>
							<Icon
								name="ChevronDown"
								className={`h-4 w-4 transition-transform ${isOpen ? "rotate-180" : ""}`}
								size={16}
							/>
						</button>
					)}
				</div>

				{hasChildren && isOpen && (
					<div className="mt-1 ml-4 border-l-2 border-border pl-2">
						{item.children?.map((child) =>
							renderMobileMenuItem(child, level + 1),
						)}
					</div>
				)}
			</div>
		)
	}

	return (
		<header className="bg-primary dark:bg-slate-800 text-primary-foreground shadow-lg sticky top-0 z-50 w-full">
			<div className="container mx-auto px-4 sm:px-6 py-3 flex justify-between items-center">
				<Link href="/" className="text-2xl font-bold flex items-center">
					<Icon
						name={siteSettings?.logo}
						alt={siteSettings?.siteName}
						className="h-8 w-8 mr-2"
						size={32}
					/>
					<span>{siteSettings?.siteName}</span>
				</Link>

				{/* 桌面导航 */}
				<nav className="hidden md:flex space-x-4 text-lg">
					{navItem?.map((item) => renderMenuItem(item))}
				</nav>

				{/* 右侧工具栏 */}
				<div className="flex items-center space-x-2">
					{/* 搜索功能 */}
					<HeaderSearch />

					<ThemeToggle />
					<LanguageSelector languages={siteSettings?.languangesNames || {}} />

					{/* 移动菜单按钮 */}
					<button
						type="button"
						className="md:hidden text-primary-foreground p-1"
						onClick={toggleMenu}
						aria-label="Toggle menu"
					>
						<Icon name="Menu" className="h-6 w-6" size={24} />
					</button>
				</div>
			</div>

			{/* 移动菜单 */}
			{isMenuOpen && (
				<div className="fixed inset-0 bg-background/80 backdrop-blur-sm z-50 md:hidden">
					<div className="fixed inset-y-0 right-0 w-full max-w-sm bg-background shadow-xl animate-in slide-in-from-right">
						<div className="flex items-center justify-between p-4 border-b">
							<h2 className="text-lg font-semibold">
								{siteSettings?.siteName}
							</h2>
							<button
								type="button"
								className="p-2 rounded-full hover:bg-accent"
								onClick={toggleMenu}
								aria-label="Close menu"
							>
								<Icon name="X" className="h-5 w-5" size={20} />
							</button>
						</div>
						<div className="overflow-y-auto h-[calc(100vh-4rem)] py-4">
							{/* 移动端搜索 */}
							<div className="px-4 mb-4">
								<HeaderSearch />
							</div>

							{navItem?.map((item) => renderMobileMenuItem(item))}

							<div className="mt-6 px-4 pt-6 border-t border-border">
								<div className="flex justify-between items-center">
									<ThemeToggle />
									<LanguageSelector
										languages={siteSettings?.languangesNames || {}}
									/>
								</div>
							</div>
						</div>
					</div>
				</div>
			)}
		</header>
	)
}
