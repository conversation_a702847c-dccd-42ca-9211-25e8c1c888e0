// lib/components/view/GameIframe.tsx
"use client"

import { cn } from "@/lib/utils/react"
import { Heart, Maximize, MessageSquare, Share2 } from "lucide-react"
import { useTranslations } from "next-intl"
import {
	forwardRef,
	useEffect,
	useImperativeHandle,
	useRef,
	useState,
	useCallback,
} from "react"
import { siteSettings } from "@/lib/config/siteSettings"
import { GameLocaleContent } from "@/lib/types/api-types"
import { useFavoriteGame } from "./GamePageContent/hooks/useFavoriteGame"

interface GameIframeProps {
	gameUrl: string
	gameTitle: string
	gameImage?: string
	description?: string
	pageName?: string | null
	playGameButtonText?: string
	loadingTitle?: string
	displayMode?: "iframe" | "popup" | "download"
	downloadUrl?: string
	// GamePlayerInfo 相关 props
	ratingElement?: React.ReactNode
	showPlayerInfo?: boolean // 控制是否显示操作栏
	// 游戏数据用于内部事件处理
	gameId?: string
	gameLocaleContent?: GameLocaleContent
}

export interface GameIframeRef {
	toggleFullscreen: () => void
}

export const GameIframe = forwardRef<GameIframeRef, GameIframeProps>(
	(
		{
			gameUrl,
			gameTitle,
			gameImage,
			description,
			pageName,
			playGameButtonText,
			loadingTitle,
			displayMode = "iframe",
			downloadUrl,
			// GamePlayerInfo 相关 props
			ratingElement,
			showPlayerInfo = true,
			// 游戏数据用于内部事件处理
			gameId,
			gameLocaleContent,
		},
		ref,
	) => {
		const [iframeLoaded, setIframeLoaded] = useState(false)
		const [showIframeOnly, setShowIframeOnly] = useState(false)
		const iframeRef = useRef<HTMLIFrameElement>(null)
		const t = useTranslations("Game")
		const loadingTitleText = loadingTitle || t("loadingTitle")

		// 使用收藏 hook（仅在有 gameId 时使用）
		const { isFavorited, handleFavorite } = useFavoriteGame(gameId || "")

		useEffect(() => {
			// 页面加载后3秒自动加载iframe
			const timer = setTimeout(() => {
				setIframeLoaded(true)
			}, 3000)

			return () => clearTimeout(timer)
		}, [])

		// 处理全屏显示的方法
		const toggleFullscreen = () => {
			if (!iframeRef.current) return

			if (document.fullscreenElement) {
				document.exitFullscreen().catch((err) => {
					console.error(`退出全屏时出错: ${err.message}`)
				})
			} else {
				iframeRef.current.requestFullscreen().catch((err) => {
					console.error(`进入全屏时出错: ${err.message}`)
				})
			}
		}

		// 处理分享功能
		const handleShare = useCallback(() => {
			if (!gameLocaleContent) return

			// 获取当前游戏信息
			const gameDescription = gameLocaleContent.gameDescription || ""
			const currentUrl = typeof window !== "undefined" ? window.location.href : ""

			// 使用站点的Twitter配置
			const twitterHandle = siteSettings?.socialLinks?.twitter
				? new URL(siteSettings.socialLinks.twitter).pathname.replace(/\//g, "")
				: ""

			// 构建Twitter分享URL
			const twitterShareUrl = new URL("https://twitter.com/intent/tweet")
			const params = new URLSearchParams()

			// 添加分享文本
			params.append(
				"text",
				`${gameTitle} - ${gameDescription.substring(0, 100)}${gameDescription.length > 100 ? "..." : ""}`,
			)

			// 添加分享链接
			params.append("url", currentUrl)

			// 如果有Twitter账号，添加via参数
			if (twitterHandle) {
				params.append("via", twitterHandle)
			}

			// 添加标签
			if (gameLocaleContent.gameTags && gameLocaleContent.gameTags.length > 0) {
				const hashtags = gameLocaleContent.gameTags
					.slice(0, 3) // 最多取3个标签
					.map((tag) => tag.name.replace(/\s+/g, ""))
					.join(",")
				if (hashtags) {
					params.append("hashtags", hashtags)
				}
			}

			twitterShareUrl.search = params.toString()

			// 打开分享窗口
			window.open(
				twitterShareUrl.toString(),
				"_blank",
				"width=550,height=420,resizable=yes,scrollbars=yes",
			)
		}, [gameTitle, gameLocaleContent])

		// 处理评论功能
		const handleComment = useCallback(() => {
			console.log("暂不实现")
		}, [])

		// 将iframe引用和方法暴露给外部组件
		useImperativeHandle(ref, () => ({
			toggleFullscreen,
		}))

		// 将iframe引用传递给其他组件或处理逻辑
		useEffect(() => {
			// 确保iframe已加载并且引用有效
			if (iframeLoaded && iframeRef.current) {
				// 可以在这里添加其他iframe加载后的逻辑
			}
		}, [iframeLoaded])

		const handlePlayClick = () => {
			if (displayMode === "download" && downloadUrl) {
				window.location.href = downloadUrl
			} else if (displayMode === "popup") {
				// Popup mode is handled by the PopupButton component
			} else {
				setShowIframeOnly(true)
			}
		}

		// 渲染 GamePlayerInfo 操作栏
		const renderPlayerInfo = () => {
			if (!showPlayerInfo) return null

			return (
				<div className="p-4 bg-card/50 backdrop-blur-sm">
					<div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
						<div className="flex-grow w-full">
							{/* 游戏标题、评分和操作按钮行 */}
							<div className="flex flex-wrap items-center justify-between gap-4">
								<div className="flex items-center gap-4">
									<h2 className="text-xl font-bold text-foreground">
										{gameTitle}
									</h2>
									{ratingElement && (
										<div className="flex-shrink-0">
											{ratingElement}
										</div>
									)}
								</div>
								<div className="flex space-x-2">
									<button
										className="text-primary p-2 rounded-full flex items-center justify-center hover:bg-muted/80 transition-all duration-200 disabled:opacity-70 disabled:cursor-not-allowed shadow-sm hover:shadow-md"
										aria-label="Fullscreen"
										type="button"
										onClick={toggleFullscreen}
									>
										<Maximize className="h-5 w-5" />
									</button>
									<button
										className="text-primary p-2 rounded-full flex items-center justify-center hover:bg-muted/80 transition-all duration-200 disabled:opacity-70 disabled:cursor-not-allowed shadow-sm hover:shadow-md"
										aria-label="Share"
										type="button"
										onClick={handleShare}
									>
										<Share2 className="h-5 w-5" />
									</button>
									{gameId && (
										<button
											className={cn(
												"p-2 rounded-full flex items-center justify-center hover:bg-muted/80 transition-all duration-200 disabled:opacity-70 disabled:cursor-not-allowed shadow-sm hover:shadow-md",
												isFavorited ? "text-red-500" : "text-primary",
											)}
											aria-label="Favorite"
											type="button"
											onClick={handleFavorite}
										>
											<Heart
												className={cn(
													"h-5 w-5",
													isFavorited ? "fill-red-500" : "",
												)}
											/>
										</button>
									)}
									<button
										className="text-primary p-2 rounded-full flex items-center justify-center hover:bg-muted/80 transition-all duration-200 disabled:opacity-70 disabled:cursor-not-allowed shadow-sm hover:shadow-md"
										aria-label="Comments"
										type="button"
										onClick={handleComment}
									>
										<MessageSquare className="h-5 w-5" />
									</button>
								</div>
							</div>
						</div>
					</div>
				</div>
			)
		}

		const renderInitialContent = () => (
			<div className="absolute inset-0 z-20 flex flex-col border border-primary items-center justify-center bg-background/90 dark:bg-background/70 backdrop-blur-md rounded-2xl overflow-hidden">
				{/* 渐变背景层 - 改进的多层次渐变 */}
				<div className="absolute inset-0 bg-gradient-to-br from-primary/30 via-background/40 to-secondary/30 dark:from-primary/20 dark:via-background/30 dark:to-secondary/20 rounded-2xl" />
				<div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top_right,theme(colors.primary.500/20),transparent_70%)] dark:bg-[radial-gradient(ellipse_at_top_right,theme(colors.primary.600/15),transparent_70%)]" />
				<div className="absolute inset-0 bg-[radial-gradient(ellipse_at_bottom_left,theme(colors.secondary/20),transparent_70%)] dark:bg-[radial-gradient(ellipse_at_bottom_left,theme(colors.secondary/15),transparent_70%)]" />

				{/* 动态发光效果层 */}
				<div className="absolute top-0 left-1/4 w-full h-1/2 bg-primary/15 dark:bg-primary/10 rotate-12 transform-gpu blur-2xl opacity-50 animate-pulse" />
				<div className="absolute bottom-0 right-1/4 w-full h-1/2 bg-secondary/15 dark:bg-secondary/10 -rotate-12 transform-gpu blur-2xl opacity-50 animate-pulse delay-200" />
				<div className="absolute top-1/4 right-0 w-1/2 h-1/2 bg-primary/15 dark:bg-primary/10 rotate-45 transform-gpu blur-2xl opacity-50 animate-pulse delay-400" />
				<div className="absolute bottom-1/4 left-0 w-1/2 h-1/2 bg-secondary/15 dark:bg-secondary/10 -rotate-45 transform-gpu blur-2xl opacity-50 animate-pulse delay-600" />

				{/* 轻微的光线纹理 */}
				<div className="absolute inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjxkZWZzPjxwYXR0ZXJuIGlkPSJncmlkIiB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHBhdHRlcm5Vbml0cz0idXNlclNwYWNlT25Vc2UiPjxwYXRoIGQ9Ik0gNDAgMCBMIDAgMCAwIDQwIiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIwLjUiIG9wYWNpdHk9IjAuMSIvPjwvcGF0dGVybj48L2RlZnM+PHJlY3Qgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgZmlsbD0idXJsKCNncmlkKSIgLz48L3N2Zz4=')] opacity-5" />

				{/* 内容层 */}
				<div className="relative z-10 w-full mx-auto px-4 md:px-8">
					<div className="flex flex-col items-center gap-2 md:gap-8">
						<div
							className="relative group w-full max-w-[220px] md:max-w-[480px] cursor-pointer"
							onClick={handlePlayClick}
						>
							<div className="w-full aspect-video rounded-2xl overflow-hidden border border-primary/70 backdrop-blur-sm shadow-xl">
								{gameImage ? (
									<img
										src={gameImage}
										alt={gameTitle}
										width={480}
										height={270}
										className="w-full h-full object-cover will-change-transform group-hover:scale-110 transition-transform duration-300 rounded-xl mx-auto"
									/>
								) : (
									<div className="w-full h-full bg-primary/20 backdrop-blur-sm flex items-center justify-center">
										<svg
											className="w-12 h-12 text-primary"
											fill="none"
											viewBox="0 0 24 24"
											stroke="currentColor"
										>
											<title>{t("playGame")}</title>
											<path
												strokeLinecap="round"
												strokeLinejoin="round"
												strokeWidth={2}
												d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z"
											/>
										</svg>
									</div>
								)}
							</div>
							{/* 图片下方的发光效果 - 增强的双层发光 */}
							<div className="absolute inset-0 rounded-2xl bg-primary/80 dark:bg-primary/70 blur-xl -z-10 group-hover:bg-primary/90 dark:group-hover:bg-primary/80 transition-colors duration-300" />
							<div className="absolute inset-0 rounded-2xl bg-secondary/60 dark:bg-secondary/50 blur-2xl -z-20 group-hover:bg-secondary/70 dark:group-hover:bg-secondary/60 transition-colors duration-300 translate-y-2 translate-x-2" />
						</div>

						<div className="text-center">
							<div className="text-base md:text-5xl font-bold text-foreground mb-4 md:mb-8 leading-tight drop-shadow-sm">
								{gameTitle}
							</div>
							<div className="flex justify-center">
								<button
									type="button"
									onClick={handlePlayClick}
									className="group relative flex items-center gap-2 md:gap-3 text-primary-foreground px-4 py-2 md:py-2 rounded-full font-bold text-lg md:text-xl transition-all duration-300 transform hover:scale-105"
								>
									<div className="absolute inset-0 rounded-full bg-gradient-to-r from-primary to-secondary group-hover:from-primary/90 group-hover:to-secondary/90 transition-all duration-300 shadow-lg" />

									<span className="relative flex items-center justify-center w-8 h-8 md:w-12 md:h-12 rounded-full bg-background/50 group-hover:bg-background/70 transition-colors backdrop-blur-sm">
										<svg
											xmlns="http://www.w3.org/2000/svg"
											className="h-8 w-8 md:h-12 md:w-12"
											fill="none"
											viewBox="0 0 24 24"
											stroke="currentColor"
										>
											<title>{t("playGame")}</title>
											<path
												strokeLinecap="round"
												strokeLinejoin="round"
												strokeWidth={2}
												d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z"
											/>
										</svg>
									</span>
									<span className="relative">
										{playGameButtonText || t("playGame")}
									</span>
								</button>
							</div>
						</div>
					</div>
				</div>
			</div>
		)

		// Popup Button Component
		const PopupButton = () => {
			const t = useTranslations()

			// Try to get the translation, fallback to default if not found
			const buttonText = playGameButtonText || t("playGame")

			return (
				<button
					type="button"
					onClick={(e) => {
						e.preventDefault()
						const width = 1024
						const height = 768
						const left = (screen.width - width) / 2
						const top = (screen.height - height) / 2
						const popupWindow = window.open(
							gameUrl,
							"_blank",
							`width=${width},height=${height},left=${left},top=${top}`,
						)
						if (popupWindow) {
							popupWindow.focus()
						}
					}}
					className="bg-warning hover:bg-warning-hover text-background font-bold py-4 px-8 rounded-full transition-all duration-300 transform hover:scale-105 flex items-center gap-2"
				>
					<svg
						className="icon"
						viewBox="0 0 1024 1024"
						version="1.1"
						xmlns="http://www.w3.org/2000/svg"
						p-id="4289"
						width="24"
						height="32"
					>
						<title>play</title>
						<path
							d="M92.59163 7.124244v175.409681l588.077547 329.465052-460.111316 245.981852V402.176952l-127.966231-71.344766v686.04357l838.81674-504.876779z"
							fill="currentColor"
							p-id="4290"
						></path>
					</svg>
					{buttonText}
				</button>
			)
		}

		// Download Button Component
		const DownloadButton = () => {
			const t = useTranslations()

			// Try to get the translation, fallback to default if not found
			const buttonText =
				playGameButtonText || t("DownloadGame") || "Download Game"

			return (
				<a
					href={downloadUrl || "#"}
					rel="noopener noreferrer"
					className="bg-primary hover:bg-primary-hover text-primary-foreground font-bold py-4 px-8 rounded-full transition-all duration-300 transform hover:scale-105 flex items-center gap-2"
				>
					<svg
						xmlns="http://www.w3.org/2000/svg"
						className="h-6 w-6"
						fill="none"
						viewBox="0 0 24 24"
						stroke="currentColor"
					>
						<title>download</title>
						<path
							strokeLinecap="round"
							strokeLinejoin="round"
							strokeWidth={2}
							d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"
						/>
					</svg>
					{buttonText}
				</a>
			)
		}

		// Render the appropriate component based on displayMode
		const renderGameComponent = () => {
			if (displayMode === "popup") {
				return <PopupButton />
			} else if (displayMode === "download") {
				return <DownloadButton />
			} else {
				return (
					<div className="w-full flex flex-col">
						<div className="w-full h-[400px] md:h-[600px] md:min-h-[600px] rounded-t-2xl relative overflow-hidden">
							{!showIframeOnly && renderInitialContent()}

							{iframeLoaded && (
								<div
									className={`w-full h-full ${!showIframeOnly ? "absolute inset-0 z-10" : ""}`}
								>
									<div className="w-full h-full bg-card rounded-t-2xl overflow-hidden shadow-lg border border-border border-b-0">
										<iframe
											ref={iframeRef}
											title={gameTitle}
											src={gameUrl}
											id="iframe-container"
											allow="accelerometer; gyroscope; autoplay; payment; fullscreen; microphone; clipboard-read; clipboard-write"
											sandbox="allow-forms allow-modals allow-orientation-lock allow-pointer-lock allow-popups allow-presentation allow-scripts allow-same-origin allow-downloads allow-popups-to-escape-sandbox"
											className="w-full h-full bg-background border-0 rounded-t-2xl"
											allowFullScreen
											loading="lazy"
										/>
									</div>
								</div>
							)}

							{!iframeLoaded && showIframeOnly && (
								<div className="w-full h-full">
									<div className="w-full h-full flex items-center justify-center bg-background/90 backdrop-blur-md rounded-t-2xl border border-border border-b-0">
										<div className="text-center">
											<div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-primary mx-auto mb-4"></div>
											<p className="text-primary text-lg">{loadingTitleText}</p>
										</div>
									</div>
								</div>
							)}
						</div>

						{/* 集成的操作栏 - 与iframe容器视觉统一 */}
						{showPlayerInfo && (
							<div className="bg-card rounded-b-2xl border border-border border-t-0 shadow-lg backdrop-blur-sm">
								{renderPlayerInfo()}
							</div>
						)}
					</div>
				)
			}
		}

		return <div className="w-full flex flex-col">{renderGameComponent()}</div>
	},
)

export default GameIframe
