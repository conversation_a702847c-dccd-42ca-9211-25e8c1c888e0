"use client"

import React from "react"
import { useTranslations } from "next-intl"
import { Folder, Tag, X } from "lucide-react"
import { GameCategory, GameTag } from "@/lib/types"
import { cn } from "@/lib/utils/react/styles"
import { Icon } from "@/lib/components/common/Icon"

interface GameCategorySidebarProps {
	categories: (GameCategory & { count: number })[]
	tags: (GameTag & { count: number })[]
	selectedCategory: string
	selectedTag: string
	onCategoryChange: (categoryCode: string) => void
	onTagChange: (tagSlug: string) => void
	className?: string
}

export const GameCategorySidebar: React.FC<GameCategorySidebarProps> = ({
	categories,
	tags,
	selectedCategory,
	selectedTag,
	onCategoryChange,
	onTagChange,
	className,
}) => {
	const t = useTranslations()

	return (
		<div className={cn("bg-card rounded-lg p-4 space-y-6", className)}>
			{/* 分类部分 */}
			<div>
				<div className="flex items-center gap-2 mb-4">
					<Folder className="h-5 w-5 text-primary" />
					<h3 className="font-semibold text-foreground">
						{t("GameList.categories")}
					</h3>
				</div>

				<div className="space-y-2">
					{/* 全部分类选项 */}
					<button
						onClick={() => onCategoryChange("")}
						className={cn(
							"w-full text-left px-3 py-2 rounded-lg transition-colors",
							!selectedCategory
								? "bg-primary text-primary-foreground"
								: "text-foreground hover:bg-muted"
						)}
					>
						<div className="flex items-center justify-between">
							<span>{t("GameList.allCategories")}</span>
							<span className={cn(
								"text-xs px-2 py-1 rounded",
								!selectedCategory
									? "bg-primary-foreground/20"
									: "bg-muted text-muted-foreground"
							)}>
								{categories.reduce((sum, cat) => sum + cat.count, 0)}
							</span>
						</div>
					</button>

					{/* 分类列表 */}
					{categories.map((category) => (
						<button
							key={category.code}
							onClick={() => onCategoryChange(category.code)}
							className={cn(
								"w-full text-left px-3 py-2 rounded-lg transition-colors",
								selectedCategory === category.code
									? "bg-primary text-primary-foreground"
									: "text-foreground hover:bg-muted"
							)}
						>
							<div className="flex items-center justify-between">
								<div className="flex items-center gap-2">
									{category.icon && (
										<Icon name={category.icon} size={16}></Icon>
									)}
									<span className="text-sm">{category.name}</span>
								</div>
								<span className={cn(
									"text-xs px-2 py-1 rounded",
									selectedCategory === category.code
										? "bg-primary-foreground/20"
										: "bg-muted text-muted-foreground"
								)}>
									{category.count}
								</span>
							</div>
						</button>
					))}
				</div>
			</div>

			{/* 标签部分 */}
			{tags.length > 0 && (
				<div>
					<div className="flex items-center gap-2 mb-4">
						<Tag className="h-5 w-5 text-primary" />
						<h3 className="font-semibold text-foreground">
							{t("GameList.tags")}
						</h3>
					</div>

					<div className="space-y-2">
						{/* 全部标签选项 */}
						<button
							onClick={() => onTagChange("")}
							className={cn(
								"w-full text-left px-3 py-2 rounded-lg transition-colors",
								!selectedTag
									? "bg-primary text-primary-foreground"
									: "text-foreground hover:bg-muted"
							)}
						>
							<div className="flex items-center justify-between">
								<span>{t("GameList.allTags")}</span>
								<span className={cn(
									"text-xs px-2 py-1 rounded",
									!selectedTag
										? "bg-primary-foreground/20"
										: "bg-muted text-muted-foreground"
								)}>
									{tags.reduce((sum, tag) => sum + tag.count, 0)}
								</span>
							</div>
						</button>

						{/* 标签列表 */}
						{tags.map((tag) => (
							<button
								key={tag.slug}
								onClick={() => onTagChange(tag.slug)}
								className={cn(
									"w-full text-left px-3 py-2 rounded-lg transition-colors",
									selectedTag === tag.slug
										? "bg-primary text-primary-foreground"
										: "text-foreground hover:bg-muted"
								)}
							>
								<div className="flex items-center justify-between">
									<div className="flex items-center gap-2">
										{tag.iconName && (
											<span className="text-sm">{tag.iconName}</span>
										)}
										<span className="text-sm">{tag.name}</span>
									</div>
									<span className={cn(
										"text-xs px-2 py-1 rounded",
										selectedTag === tag.slug
											? "bg-primary-foreground/20"
											: "bg-muted text-muted-foreground"
									)}>
										{tag.count}
									</span>
								</div>
							</button>
						))}
					</div>
				</div>
			)}

			{/* 当前筛选条件 */}
			{(selectedCategory || selectedTag) && (
				<div>
					<h4 className="font-medium text-foreground mb-3">
						{t("GameList.activeFilters")}
					</h4>
					<div className="space-y-2">
						{selectedCategory && (
							<div className="flex items-center justify-between bg-muted px-3 py-2 rounded-lg">
								<span className="text-sm text-foreground">
									{categories.find(cat => cat.code === selectedCategory)?.name}
								</span>
								<button
									onClick={() => onCategoryChange("")}
									className="text-muted-foreground hover:text-foreground"
								>
									<X className="h-4 w-4" />
								</button>
							</div>
						)}
						{selectedTag && (
							<div className="flex items-center justify-between bg-muted px-3 py-2 rounded-lg">
								<span className="text-sm text-foreground">
									{tags.find(tag => tag.slug === selectedTag)?.name}
								</span>
								<button
									onClick={() => onTagChange("")}
									className="text-muted-foreground hover:text-foreground"
								>
									<X className="h-4 w-4" />
								</button>
							</div>
						)}
					</div>
				</div>
			)}
		</div>
	)
}
