"use client"
import React, { useMemo } from "react"
import GameIframe from "../GameIframe"
import ContentAd from "./components/ContentAd"

import { getGameLocaleContent } from "@/lib/services/api-client"
// 导入API类型定义
import {
	GameDetailContent,
	GameDetailContentType,
	GameInfoData,
	GameLocaleContent,
	GameTag,
	ProjectGame,
} from "@/lib/types/api-types"
import { useTranslations } from "next-intl"
import RightSidebar from "../RightSidebar"
import ContentNavigationTabs from "./components/ContentNavigationTabs"
import FloatingAction from "./components/FloatingAction"
// 导入新的视图组件
import RelatedGamesContent from "./components/RelatedGamesContent"
import CommentsContent from "./components/CommentsContent"
import TabContentRenderer from "./components/TabContentRenderer"
// 导入新的子组件
import GameHeader from "./components/GameHeader"
import GameInfoSection from "./components/GameInfoSection"
import StarRating from "./components/StarRating"

interface AdCode {
	id: string
	position: string
	code: string
}

// Props Interface
interface GamePageContentProps {
	locale: string
	projectGame: ProjectGame // 直接从服务端传入游戏信息
	gameLocaleContent: GameLocaleContent // 直接从服务端传入本地化内容
	adCodes?: AdCode[] // 广告代码，可选
	allGames?: ProjectGame[] // 所有游戏信息，用于获取关联游戏的详细信息
}

// 注意：图标映射已移至ContentNavigationTabs组件内部

export const GamePageContent: React.FC<GamePageContentProps> = ({
	locale,
	projectGame,
	gameLocaleContent,
	adCodes = [],
	allGames = [],
}) => {
	const t = useTranslations("Game")
	// 从传入的数据中提取所需信息
	const gameName = gameLocaleContent.gameName
	const gameSlogan = gameLocaleContent.gameSlogan
	// 推荐游戏卡片数据 - 从allGames中获取关联游戏的详细信息
	const gameCards: GameLocaleContent[] = useMemo(() => {
		if (!projectGame.relatedGames || projectGame.relatedGames.length === 0) {
			return []
		}
		return projectGame.relatedGames
			.map((gameId) => {
				// 在allGames中查找对应的游戏
				const relatedGame = allGames.find((game) => game.id === gameId)
				// 如果找到了游戏，使用其详细信息
				if (relatedGame) {
					// 查找对应语言的本地化内容
					const localeContent: GameLocaleContent = getGameLocaleContent(
						locale,
						relatedGame,
					)

					return localeContent
				}
				console.log(`获取推荐游戏列表时，没有找到游戏:${gameId} 对应的游戏详情`)
				return {} as GameLocaleContent
			})
			.filter((game) => game.id !== null)
	}, [projectGame.relatedGames, allGames, locale])

	// 游戏iframe数据
	const gameIframeData: GameInfoData = projectGame.gameInfo

	// 游戏标签
	const tags: GameTag[] = gameLocaleContent.gameTags || []

	// 游戏tabs内容（包含推荐游戏、是否显示评论、是否显示相关视频）
	// 数据在api服务端已经拼接好了
	const tabContents: GameDetailContent[] = gameLocaleContent.contents.filter(
		(tab) => tab.type !== GameDetailContentType.RelatedGames,
	)

	// 针对推荐游戏、相关视频、评论，这三个标签页的icon和title需要补充国际化
	tabContents.map((tab) => {
		if (tab.type === GameDetailContentType.RelatedGames) {
			tab.icon = "star"
			tab.title = t("relatedGames")
		}
		if (tab.type === GameDetailContentType.RelatedVideos) {
			tab.icon = "video"
			tab.title = t("relatedVideos")
		}
		if (tab.type === GameDetailContentType.Comments) {
			tab.icon = "comment"
			tab.title = t("comments")
		}
		return tab
	})
	// 游戏视频
	const gameVideos = projectGame.relatedVideos || []

	// 游戏评分
	const gameRating = projectGame.gameInfo?.settings?.rating || 4.5

	// --- Prepare items for the 8-slot recommendation grid ---
	const gridItems = useMemo(() => {
		const numTotalSlots = 8
		const numGamesToConsider = 6
		const gamesToDisplay = gameCards.slice(0, numGamesToConsider)
		const items = Array(numTotalSlots).fill(null)

		// 只有当游戏总数大于等于7个时才显示"查看更多"选项
		const shouldShowViewMore = gameCards.length >= 7
		const viewMoreSlotIndex = numTotalSlots - 1 // 最后一个位置用于"查看更多"

		// 如果游戏数量超过考虑数量，添加广告
		if (gamesToDisplay.length > numGamesToConsider) {
			const adSlotIndex = 6 // 广告固定在倒数第二个位置
			items[adSlotIndex] = { type: "ad", id: "ad-slot" }
		}

		// 只有当游戏总数大于等于7个时才添加"查看更多"选项
		if (shouldShowViewMore) {
			items[viewMoreSlotIndex] = { type: "viewMore", id: "view-more-slot" }
		}

		// Fill remaining slots with games or empty placeholders
		let gameDataIndex = 0
		for (let i = 0; i < numTotalSlots; i++) {
			if (items[i] === null) {
				// If the slot is not yet filled
				if (gameDataIndex < gamesToDisplay.length) {
					const game = gamesToDisplay[gameDataIndex]
					if (game) {
						items[i] = { type: "game", ...game, id: game.id }
						gameDataIndex++
					}
				} else {
					items[i] = { type: "empty", id: `empty-${i}` }
				}
			}
		}
		return items
	}, [gameCards]) // Recalculate if gameCards changes

	// 数据检查 - 由于数据是从服务端传入，不需要加载状态和错误处理
	// 但仍然检查数据是否存在
	if (!gameIframeData) {
		return (
			<div className="flex justify-center items-center min-h-[400px] text-muted-foreground">
				{t("noGameData")}
			</div>
		)
	}

	return (
		<div className="mb-10">
			{/* 主要内容区域 */}
			<div className="flex flex-col lg:flex-row gap-4 lg:gap-6">
				<div className="w-full lg:w-3/4 flex flex-col">
					{/* 游戏标题和基本信息区域 */}
					<GameHeader gameName={gameName} gameSlogan={gameSlogan} />

					{/* Game Iframe with integrated Player Info */}
					<GameIframe
						gameUrl={gameIframeData.gameUrl}
						gameTitle={gameName}
						gameImage={gameLocaleContent.gameImages?.[0] || ""}
						description={gameLocaleContent.gameDescription}
						// GamePlayerInfo 相关 props
						ratingElement={<StarRating rating={gameRating} />}
						showPlayerInfo={true}
						// 传递游戏数据用于内部事件处理
						gameId={projectGame.id}
						gameLocaleContent={gameLocaleContent}
					/>
					<ContentAd position="content-middle" adCodes={adCodes} />

					<RelatedGamesContent gridItems={gridItems} />

					{/* Content Navigation Tabs */}
					<ContentNavigationTabs tabs={tabContents} />

					{/* Tab 内容区域 */}
					<div className="grid grid-cols-1 xl:grid-cols-5 gap-4 lg:gap-6">
						<div className="space-y-6 lg:space-y-10 lg:col-span-3">
							{/* 游戏信息段落 - 游戏设置和标签 */}
							<div className="bg-card rounded-xl shadow-md overflow-hidden p-4 sm:p-6">
								<GameInfoSection projectGame={projectGame} tags={tags} />
							</div>

							{/* 动态渲染Tab内容 - 根据tab.type类型渲染不同内容 */}
							{tabContents.map((tab) => (
								<TabContentRenderer
									key={tab.tabId}
									tab={tab}
									gameVideos={gameVideos}
								/>
							))}
						</div>

						{/* 评论区域 - 在大屏幕上作为第二列，小屏幕上在底部 */}
						<div className="xl:sticky xl:top-4 xl:self-start lg:col-span-2">
							<CommentsContent />
						</div>
					</div>
				</div>

				{/* Right Sidebar - 只在大屏幕显示 */}
				<div className="hidden lg:block lg:w-1/4">
					<RightSidebar allGames={allGames} locale={locale} />
				</div>
			</div>

			{/* Floating Action Buttons */}
			<FloatingAction />
		</div>
	)
}
