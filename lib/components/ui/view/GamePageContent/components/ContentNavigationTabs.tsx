"use client"
import React, { useEffect } from "react"
import { Icon } from "@/lib/components/common/Icon"
import { GameDetailContent, GameDetailContentType } from "@/lib/types/api-types"
import { useTranslations } from "next-intl"

interface ContentNavigationTabsProps {
	tabs: GameDetailContent[]
	activeTabId?: string // Optional: Control active tab from parent
	onTabClick?: (tabId: string) => void // Optional: Handle tab clicks in parent
}

export const ContentNavigationTabs: React.FC<ContentNavigationTabsProps> = ({
	tabs = [],
	activeTabId, // Use this if provided
	onTabClick,
}) => {
	console.log("tabs", tabs)

	// 如果没有提供 activeTabId，则使用第一个标签页的 id
	const currentActiveTabId = activeTabId ?? tabs[0]?.tabId
	
	// 处理平滑滚动到对应内容区域
	const scrollToContent = (tabId: string) => {
		const element = document.getElementById(tabId)
		if (element) {
			// 使用平滑滚动效果
			element.scrollIntoView({
				behavior: "smooth",
				block: "start"
			})
		}
	}
	
	// 如果URL中包含hash，初始化时滚动到对应位置
	useEffect(() => {
		if (window.location.hash) {
			const hash = window.location.hash.substring(1) // 去掉#号
			const element = document.getElementById(hash)
			if (element) {
				// 添加一点延迟，确保页面完全加载
				setTimeout(() => {
					element.scrollIntoView({
						behavior: "smooth",
						block: "start"
					})
				}, 300)
			}
		}
	}, [])

	return (
		<div className="mb-8">
			<div className="border-b border-gray-200 dark:border-gray-700">
				{/* 使用grid布局平铺显示，兼容移动端 */}
				<ul
					className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-1 -mb-px text-sm font-medium text-center"
					id="content-tabs"
				>
					{tabs && (tabs.map((tab) => {
						const isActive = tab.tabId === currentActiveTabId
						return (
							<li className="w-full" key={tab.tabId}>
								<a
									href={`#${tab.tabId}`} // Keep href for accessibility and non-JS fallback
									onClick={(e) => {
										// 总是阻止默认行为，使用自定义滚动
										e.preventDefault()
										
										// 如果提供了外部处理函数，则调用
										if (onTabClick) {
											onTabClick(tab.tabId)
										}
										
										// 平滑滚动到目标位置
										scrollToContent(tab.tabId)
									}}
									className={`inline-flex items-center justify-center p-2 sm:p-3 md:p-4 border-b-2 rounded-t-lg group whitespace-nowrap w-full ${
										isActive
											? "border-primary text-primary dark:border-primary dark:text-primary active"
											: "border-transparent hover:text-gray-600 hover:border-gray-300 dark:hover:text-gray-300 dark:hover:border-gray-600"
									}`}
									aria-current={isActive ? "page" : undefined}
								>
									{tab.icon && (
										<Icon
											name={tab.icon}
											className={`w-4 h-4 mr-1 ${isActive ? "text-primary dark:text-primary" : "text-gray-400 group-hover:text-gray-500 dark:text-gray-500 dark:group-hover:text-gray-300"}`}
											size={16}
										/>
									)}
									<span className="truncate">{tab.title}</span>
								</a>
							</li>
						)
					}))}
				</ul>
			</div>
		</div>
	)
}

export default ContentNavigationTabs
