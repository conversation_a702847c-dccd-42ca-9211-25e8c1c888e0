import React from "react"
import { <PERSON> } from "@i18n/navigation"
import { GameTag, ProjectGame } from "@/lib/types/api-types"
import { useTranslations } from "next-intl"

interface GameInfoSectionProps {
	projectGame: ProjectGame
	tags: GameTag[]
	className?: string
}

/**
 * 游戏信息段落组件
 * 显示游戏详细信息和标签
 */
const GameInfoSection: React.FC<GameInfoSectionProps> = ({
	projectGame,
	tags,
	className = ""
}) => {
	const t = useTranslations("Game")

	// 从游戏信息中提取出需要显示的字段
	const gameInfoItems = [
		{ label: "Developer", value: "Flow Puzzles" },
		{ label: "Rating", value: "4.1 (Player Rating)", hasStars: true },
		{ label: "Release Date", value: "May 11, 2025" },
		{ label: "Technology", value: "HTML5" },
		{ label: "Platforms", value: "Mobile, Desktop", hasPlatformIcons: true },
		{ label: "Age Rating", value: "6+" },
		{ label: "Localization", value: "English, Italian, Spanish, German, Chinese, Turkish, Russian, Japanese" },
		{ label: "Screen Orientation", value: "Portrait / Landscape" },
		{ label: "Cloud Saves", value: "No" },
		{ label: "Authorization Support", value: "Yes" }
	]

	// 游戏分类
	const categories = [
		{ name: "All Games", href: "/games" },
		{ name: "Puzzles", href: "/games/puzzles" },
		{ name: "Merge Italian Brainrot", href: "#" }
	]

	// 游戏标签
	const gameTags = [
		"Merge Game",
		"Puzzle",
		"Casual",
		"Collection",
		"Free",
		"HTML5",
		"Italian Meme",
		"Brainrot"
	]

	return (
		<div className={`game-info-section ${className}`}>
			{/* 游戏详细信息 */}
			<div className="game-details-container space-y-4">
				{gameInfoItems.map((item, index) => (
					<div key={index} className="game-info-item flex">
						<div className="info-label w-1/3 text-muted-foreground font-medium">
							{item.label}:
						</div>
						<div className="info-value w-2/3 text-foreground">
							{item.hasStars ? (
								<div className="flex items-center">
									<div className="stars text-amber-400 mr-2">
										★★★★☆
									</div>
									<span>{item.value}</span>
								</div>
							) : item.hasPlatformIcons ? (
								<div className="flex items-center">
									<span className="platform-icon bg-gray-200 text-gray-700 rounded px-1 mr-1">M</span>
									<span>Mobile,</span>
									<span className="platform-icon bg-gray-200 text-gray-700 rounded px-1 mx-1">D</span>
									<span>Desktop</span>
								</div>
							) : (
								<span>{item.value}</span>
							)}
						</div>
					</div>
				))}
			</div>

			{/* 游戏分类 */}
			<div className="game-categories mt-6">
				<h3 className="text-lg font-semibold mb-3 text-foreground">
					{t("categories") || "分类"}
				</h3>
				<div className="categories-path flex items-center text-sm">
					{categories.map((category, index) => (
						<React.Fragment key={index}>
							<Link 
								href={category.href}
								className="text-primary hover:underline"
							>
								{category.name}
							</Link>
							{index < categories.length - 1 && (
								<span className="mx-2 text-muted-foreground">{'>'}</span>
							)}
						</React.Fragment>
					))}
				</div>
			</div>

			{/* 游戏标签 */}
			<div className="game-tags mt-6">
				<h3 className="text-lg font-semibold mb-3 text-foreground">
					{t("tags") || "标签"}
				</h3>
				<div className="tags-container flex flex-wrap gap-2">
					{tags && tags.length > 0 ? (
						tags.map((tag) => (
							<Link
								key={tag.id}
								href={tag.slug}
								className="tag-item bg-primary/10 text-primary px-3 py-1.5 rounded-lg text-sm hover:bg-primary/20 transition-colors border border-primary/20 font-medium"
							>
								{tag.name}
							</Link>
						))
					) : (
						// 使用示例标签作为备用
						gameTags.map((tagName, index) => (
							<span
								key={index}
								className="tag-item bg-primary/10 text-primary px-3 py-1.5 rounded-lg text-sm border border-primary/20 font-medium"
							>
								{tagName}
							</span>
						))
					)}
				</div>
			</div>
		</div>
	)
}

export default GameInfoSection
export { GameInfoSection }
