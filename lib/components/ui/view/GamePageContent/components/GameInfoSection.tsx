import React from "react"
import { <PERSON> } from "@i18n/navigation"
import { GameTag, ProjectGame } from "@/lib/types/api-types"
import { useTranslations } from "next-intl"

interface GameInfoSectionProps {
	projectGame: ProjectGame
	tags: GameTag[]
	className?: string
}

/**
 * 游戏信息段落组件
 * 显示游戏设置信息和标签
 */
const GameInfoSection: React.FC<GameInfoSectionProps> = ({
	projectGame,
	tags,
	className = ""
}) => {
	const t = useTranslations("Game")

	return (
		<div className={`space-y-4 sm:space-y-6 ${className}`}>
			{/* 游戏设置信息 */}
			{projectGame.gameInfo.settings && (
				<div>
					<h3 className="text-lg font-semibold mb-3 text-foreground">
						{t("gameInfo")}
					</h3>
					<div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
						{Object.entries(projectGame.gameInfo.settings).map(
							([key, value]) => (
								<div key={key} className="bg-muted/50 px-3 py-2 rounded-lg">
									<span className="text-sm">
										<span className="font-medium text-foreground">
											{key}:
										</span>{" "}
										<span className="text-muted-foreground">{value}</span>
									</span>
								</div>
							),
						)}
					</div>
				</div>
			)}

			{/* 游戏标签 */}
			{tags && tags.length > 0 && (
				<div>
					<h3 className="text-lg font-semibold mb-3 text-foreground">
						{t("tags") || "标签"}
					</h3>
					<div className="flex flex-wrap gap-2">
						{tags.map((tag) => (
							<Link
								key={tag.id}
								href={tag.slug}
								className="bg-primary/10 text-primary px-3 py-1.5 rounded-lg text-sm hover:bg-primary/20 transition-colors border border-primary/20 font-medium"
							>
								{tag.name}
							</Link>
						))}
					</div>
				</div>
			)}
		</div>
	)
}

export default GameInfoSection
export { GameInfoSection }
