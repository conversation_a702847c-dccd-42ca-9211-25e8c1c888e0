"use client"
import React from "react"
import { Icon } from "@/lib/components/common"
import { Link } from "@i18n/navigation"
import { useTranslations } from "next-intl"
import GameCard from "../../GameCard"
import { GameLocaleContent } from "@/lib/types/api-types"

// 定义网格项目类型
interface GridItem extends Partial<GameLocaleContent> {
	type: "game" | "ad" | "viewMore" | "empty"
	id: string
}

interface RelatedGamesContentProps {
	gridItems: GridItem[]
}

const RelatedGamesContent: React.FC<RelatedGamesContentProps> = ({
	gridItems,
}) => {
	const t = useTranslations("Game")

	return (
		<div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-4 xl:grid-cols-4 gap-3 sm:gap-4">
			{gridItems.map((item) => {
				if (!item) return null

				switch (item.type) {
					case "game":
						const { type, ...gameProps } = item
						// 确保 gameProps 包含 GameCard 所需的所有必要属性
						if (gameProps.gameName && gameProps.gameInfo) {
							return <GameCard key={item.id} {...(gameProps as GameLocaleContent)} />
						}
						return null
					case "ad":
						return (
							<div
								key={item.id}
								className="bg-muted rounded-lg shadow-md flex items-center justify-center p-3 sm:p-4 h-full min-h-[120px] sm:min-h-[140px]"
							>
								<span className="text-muted-foreground text-center text-xs sm:text-sm">
									{t("ad")}
								</span>
							</div>
						)
					case "viewMore":
						return (
							<Link
								key={item.id}
								href="/categories"
								className="bg-card rounded-lg shadow-md hover:shadow-lg transition-shadow flex flex-col items-center justify-center p-4 text-primary hover:bg-muted group h-full"
							>
								<div className="flex flex-col items-center justify-center h-full">
									<div className="rounded-full bg-primary/10 p-4 mb-2">
										<Icon
											name="ArrowRight"
											className="h-6 w-6 text-primary group-hover:translate-x-1 transition-transform"
											size={24}
										/>
									</div>
									<span className="font-medium text-center text-sm">
										{t("viewMore")}{" "}
									</span>
								</div>
							</Link>
						)
					case "empty":
						return null
					default:
						return null
				}
			})}
		</div>
	)
}

export default RelatedGamesContent
